const { chromium } = require('playwright');
const fs = require('fs');

async function scrapeBursaries() {
    const browser = await chromium.launch({ headless: false }); // Set to true for headless mode
    const page = await browser.newPage();
    
    const allJobs = [];
    let currentPage = 1;
    
    try {
        // Navigate to the first page
        console.log('Navigating to bursaries page...');
        await page.goto('https://www.spane4all.co.za/bursaries', { waitUntil: 'domcontentloaded', timeout: 60000 });
        console.log('Page loaded, waiting for content...');

        // Wait for the card wrapper to be visible
        await page.waitForSelector('.card-wrapper', { timeout: 30000 });
        console.log('Card wrapper found!');
        
        while (true) {
            console.log(`Scraping page ${currentPage}...`);
            
            // Find all Apply buttons on the current page
            const applyButtons = await page.locator('a.btn.btn-primary.mt-2:has-text("Apply")').all();
            console.log(`Found ${applyButtons.length} Apply buttons on page ${currentPage}`);
            
            // Process each Apply button
            for (let i = 0; i < applyButtons.length; i++) {
                try {
                    // Get the href attribute before clicking
                    const href = await applyButtons[i].getAttribute('href');
                    console.log(`Processing job ${i + 1}: ${href}`);
                    
                    // Click the Apply button to navigate to job details
                    await applyButtons[i].click();
                    await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
                    
                    // Wait for the page to load and check for job details
                    await page.waitForLoadState('domcontentloaded');

                    // Wait for the card body to be visible
                    await page.waitForSelector('.card-body', { timeout: 10000 });

                    // Extract job information from the page
                    const jobData = await page.evaluate(() => {
                        const data = {};

                        // Extract job title from h1
                        const titleElement = document.querySelector('h1');
                        data.title = titleElement ? titleElement.textContent.trim() : '';

                        // Extract information from the card body
                        const cardBody = document.querySelector('.card-body');
                        if (cardBody) {
                            // Extract organization name
                            const orgElement = cardBody.querySelector('p.card-text strong');
                            data.organization = orgElement ? orgElement.textContent.trim() : '';

                            // Extract employment type, location, salary, closing date
                            const paragraphs = cardBody.querySelectorAll('p');
                            paragraphs.forEach(p => {
                                const text = p.textContent.trim();
                                if (text.includes('Employment Type:')) {
                                    data.employmentType = text.replace('Employment Type:', '').trim();
                                } else if (text.includes('Job Location:')) {
                                    data.jobLocation = text.replace('Job Location:', '').trim();
                                } else if (text.includes('Base Salary:')) {
                                    data.baseSalary = text.replace('Base Salary:', '').trim();
                                } else if (text.includes('Closing Date:')) {
                                    // Handle the closing date which might have a span element
                                    const closingDateSpan = p.querySelector('#formatted-date');
                                    if (closingDateSpan) {
                                        data.closingDate = closingDateSpan.textContent.trim();
                                    } else {
                                        data.closingDate = text.replace('Closing Date:', '').trim();
                                    }
                                }
                            });

                            // Extract image
                            const imgElement = cardBody.querySelector('img.card-img-top');
                            data.image = imgElement ? imgElement.getAttribute('src') : '';

                            // Extract Apply Now button URL
                            const applyButton = cardBody.querySelector('a.btn.btn-primary');
                            data.applyUrl = applyButton ? applyButton.getAttribute('href') : '';
                        }

                        // Get current page URL
                        data.sourceUrl = window.location.href;

                        // Extract description
                        const descriptionHeading = Array.from(document.querySelectorAll('h2')).find(el =>
                            el.textContent.trim() === 'Description'
                        );
                        if (descriptionHeading && descriptionHeading.nextElementSibling) {
                            data.description = descriptionHeading.nextElementSibling.textContent.trim();
                        }

                        return data;
                    });
                    
                    if (jobData) {
                        allJobs.push(jobData);
                        console.log(`Scraped job: ${jobData.organization || 'Unknown'}`);
                    }
                    
                    // Go back to the main page
                    await page.goBack();
                    await page.waitForLoadState('domcontentloaded', { timeout: 30000 });

                    // Wait for the card wrapper to be visible again
                    await page.waitForSelector('.card-wrapper', { timeout: 15000 });
                    
                    // Re-find the apply buttons since we navigated back
                    const updatedApplyButtons = await page.locator('a.btn.btn-primary.mt-2:has-text("Apply")').all();
                    applyButtons.length = 0;
                    applyButtons.push(...updatedApplyButtons);
                    
                } catch (error) {
                    console.error(`Error processing job ${i + 1}:`, error.message);
                    // Try to go back to main page if we're stuck
                    try {
                        await page.goBack();
                        await page.waitForLoadState('networkidle');
                    } catch (backError) {
                        console.error('Error going back:', backError.message);
                        // Navigate back to the main page
                        await page.goto('https://www.spane4all.co.za/bursaries');
                        await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
                        await page.waitForSelector('.card-wrapper', { timeout: 15000 });
                    }
                }
            }
            
            // Look for Next button to go to next page
            const nextButton = await page.locator('a:has-text("Next")').first();
            const isNextButtonVisible = await nextButton.isVisible().catch(() => false);
            
            if (isNextButtonVisible) {
                console.log(`Moving to page ${currentPage + 1}...`);
                await nextButton.click();
                await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
                await page.waitForSelector('.card-wrapper', { timeout: 15000 });
                currentPage++;
            } else {
                console.log('No more pages found. Scraping complete.');
                break;
            }
        }
        
    } catch (error) {
        console.error('Error during scraping:', error);
    } finally {
        await browser.close();
    }
    
    // Save results to JSON file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `bursaries_${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify(allJobs, null, 2));
    console.log(`\nScraping completed! Found ${allJobs.length} jobs.`);
    console.log(`Results saved to: ${filename}`);
    
    return allJobs;
}

// Run the scraper
if (require.main === module) {
    scrapeBursaries().catch(console.error);
}

module.exports = { scrapeBursaries };
