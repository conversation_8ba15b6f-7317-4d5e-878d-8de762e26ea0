const { chromium } = require('playwright');
const fs = require('fs');

async function scrapeBursaries() {
    const browser = await chromium.launch({ headless: false }); // Set to true for headless mode
    const page = await browser.newPage();
    
    const allJobs = [];
    let currentPage = 1;
    
    try {
        // Navigate to the first page
        await page.goto('https://www.spane4all.co.za/bursaries');
        await page.waitForLoadState('networkidle');
        
        while (true) {
            console.log(`Scraping page ${currentPage}...`);
            
            // Find all Apply buttons on the current page
            const applyButtons = await page.locator('a[href^="/job/"]:has-text("Apply")').all();
            console.log(`Found ${applyButtons.length} Apply buttons on page ${currentPage}`);
            
            // Process each Apply button
            for (let i = 0; i < applyButtons.length; i++) {
                try {
                    // Get the href attribute before clicking
                    const href = await applyButtons[i].getAttribute('href');
                    console.log(`Processing job ${i + 1}: ${href}`);
                    
                    // Click the Apply button to navigate to job details
                    await applyButtons[i].click();
                    await page.waitForLoadState('networkidle');
                    
                    // Wait for the page to load and check for job details
                    await page.waitForLoadState('domcontentloaded');

                    // Extract job information from the page
                    const jobData = await page.evaluate(() => {
                        const data = {};

                        // Extract job title from h1 or main heading
                        const titleElement = document.querySelector('h1') || document.querySelector('.job-title') || document.querySelector('h2');
                        data.title = titleElement ? titleElement.textContent.trim() : '';

                        // Extract organization name - look for the hiring organization section
                        const orgElements = document.querySelectorAll('h3, h4, strong, p');
                        for (let element of orgElements) {
                            const text = element.textContent.trim();
                            if (text === 'Hiring organization' && element.nextElementSibling) {
                                // Look for organization name after "Hiring organization" heading
                                let nextElement = element.nextElementSibling;
                                while (nextElement) {
                                    if (nextElement.tagName === 'P' && nextElement.querySelector('strong')) {
                                        data.organization = nextElement.querySelector('strong').textContent.trim();
                                        break;
                                    }
                                    if (nextElement.textContent.trim() && !nextElement.querySelector('img')) {
                                        data.organization = nextElement.textContent.trim();
                                        break;
                                    }
                                    nextElement = nextElement.nextElementSibling;
                                }
                            }
                        }

                        // Extract employment type, location, salary, closing date
                        const allText = document.body.textContent;
                        const lines = allText.split('\n').map(line => line.trim()).filter(line => line.length > 0);

                        for (let line of lines) {
                            if (line.includes('Employment Type:')) {
                                data.employmentType = line.replace('Employment Type:', '').trim();
                            } else if (line.includes('Job Location:')) {
                                data.jobLocation = line.replace('Job Location:', '').trim();
                            } else if (line.includes('Base Salary:')) {
                                data.baseSalary = line.replace('Base Salary:', '').trim();
                            } else if (line.includes('Closing Date:')) {
                                data.closingDate = line.replace('Closing Date:', '').trim();
                            }
                        }

                        // Extract image
                        const imgElement = document.querySelector('img[src*="uploaded_img"]');
                        data.image = imgElement ? imgElement.getAttribute('src') : '';

                        // Extract Apply Now button URL
                        const applyButton = document.querySelector('a[href*="http"]');
                        data.applyUrl = applyButton ? applyButton.getAttribute('href') : '';

                        // Get current page URL
                        data.sourceUrl = window.location.href;

                        // Extract description
                        const descriptionElement = document.querySelector('h2:contains("Description")') ||
                                                 Array.from(document.querySelectorAll('h2, h3')).find(el => el.textContent.includes('Description'));
                        if (descriptionElement && descriptionElement.nextElementSibling) {
                            data.description = descriptionElement.nextElementSibling.textContent.trim();
                        }

                        return data;
                    });
                    
                    if (jobData) {
                        allJobs.push(jobData);
                        console.log(`Scraped job: ${jobData.organization || 'Unknown'}`);
                    }
                    
                    // Go back to the main page
                    await page.goBack();
                    await page.waitForLoadState('networkidle');
                    
                    // Re-find the apply buttons since we navigated back
                    const updatedApplyButtons = await page.locator('a[href^="/job/"]:has-text("Apply")').all();
                    applyButtons.length = 0;
                    applyButtons.push(...updatedApplyButtons);
                    
                } catch (error) {
                    console.error(`Error processing job ${i + 1}:`, error.message);
                    // Try to go back to main page if we're stuck
                    try {
                        await page.goBack();
                        await page.waitForLoadState('networkidle');
                    } catch (backError) {
                        console.error('Error going back:', backError.message);
                        // Navigate back to the main page
                        await page.goto('https://www.spane4all.co.za/bursaries');
                        await page.waitForLoadState('networkidle');
                    }
                }
            }
            
            // Look for Next button to go to next page
            const nextButton = await page.locator('a:has-text("Next")').first();
            const isNextButtonVisible = await nextButton.isVisible().catch(() => false);
            
            if (isNextButtonVisible) {
                console.log(`Moving to page ${currentPage + 1}...`);
                await nextButton.click();
                await page.waitForLoadState('networkidle');
                currentPage++;
            } else {
                console.log('No more pages found. Scraping complete.');
                break;
            }
        }
        
    } catch (error) {
        console.error('Error during scraping:', error);
    } finally {
        await browser.close();
    }
    
    // Save results to JSON file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `bursaries_${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify(allJobs, null, 2));
    console.log(`\nScraping completed! Found ${allJobs.length} jobs.`);
    console.log(`Results saved to: ${filename}`);
    
    return allJobs;
}

// Run the scraper
if (require.main === module) {
    scrapeBursaries().catch(console.error);
}

module.exports = { scrapeBursaries };
