# Bursary Scraper

A Playwright-based web scraper that extracts bursary/job information from https://www.spane4all.co.za/bursaries.

## Features

- Scrapes all pages of bursary listings
- Clicks on each "Apply" button to get detailed job information
- Extracts comprehensive job data including:
  - Organization name
  - Employment type
  - Job location
  - Base salary
  - Closing date
  - Apply URL
  - Source URL
- Saves results to timestamped JSON files
- <PERSON>les pagination automatically
- Error handling and recovery

## Installation

1. Make sure you have Node.js installed
2. Install dependencies:
   ```bash
   npm install playwright
   ```
3. Install Playwright browsers:
   ```bash
   npx playwright install
   ```

## Usage

### Run the scraper directly:
```bash
node bursary-scraper.js
```

### Run the test script:
```bash
node test-scraper.js
```

### Run a quick test (first 2 pages, 3 jobs per page):
```bash
npm run quick-test
```

### Use as a module:
```javascript
const { scrapeBursaries } = require('./bursary-scraper');

async function main() {
    const jobs = await scrapeBursaries();
    console.log(`Found ${jobs.length} jobs`);
}

main();
```

## Output

The scraper saves results to a JSON file with the format: `bursaries_YYYY-MM-DDTHH-mm-ss-sssZ.json`

Each job entry contains:
```json
{
  "title": "Hiring organization",
  "organization": "South African Reserve Bank (SARB)",
  "employmentType": "Bursary",
  "jobLocation": "South Africa",
  "baseSalary": "Market Related",
  "closingDate": "31-07-2025",
  "applyUrl": "https://ttibursaries.co.za/students/fine-arts-scholarship/",
  "sourceUrl": "https://www.spane4all.co.za/job/6796",
  "image": "/uploaded_img/22173.jpeg"
}
```

## Configuration

You can modify the scraper behavior by editing `bursary-scraper.js`:

- Set `headless: true` in the browser launch options for headless mode
- Adjust timeouts and wait conditions as needed
- Modify the data extraction logic in the `page.evaluate()` function

## Error Handling

The scraper includes robust error handling:
- Continues scraping even if individual jobs fail
- Automatically navigates back to the main page if stuck
- Logs detailed error information
- Saves partial results even if the scraper encounters issues

## Notes

- The scraper runs in non-headless mode by default so you can see the browser actions
- It waits for network idle state to ensure pages are fully loaded
- Pagination is handled automatically by looking for "Next" buttons
- The scraper respects the website's structure and waits appropriately between actions
