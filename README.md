# Spane4all Job Scraper

A Playwright-based web scraper that extracts job information from various categories on https://www.spane4all.co.za.

## Features

- **Multiple Categories**: Scrape from 7 different job categories:
  - Bursaries
  - Internships
  - Jobs
  - Scholarships
  - General Jobs
  - Government Internships
  - Government Jobs
- **Interactive Mode**: Choose category and settings through prompts
- **Command Line Options**: Direct category selection with optional page limits
- **Comprehensive Metrics**: Track pages scraped, success rates, timing, and more
- **Detailed Job Data**: Extracts organization, employment type, location, salary, closing dates, and apply URLs
- **Smart Error Handling**: Continues scraping even if individual jobs fail
- **Timestamped Results**: Saves results to categorized JSON files
- **Progress Tracking**: Real-time feedback on scraping progress

## Installation

1. Make sure you have Node.js installed
2. Install dependencies:
   ```bash
   npm install playwright
   ```
3. Install Playwright browsers:
   ```bash
   npx playwright install
   ```

## Usage

### Interactive Mode (Recommended):
```bash
npm run scrape
# or
node category-scraper.js
```
This will prompt you to choose a category and page limit.

### Direct Category Scraping:
```bash
# Scrape specific categories
npm run scrape-bursaries
npm run scrape-internships
npm run scrape-jobs
npm run scrape-scholarships
npm run scrape-general
npm run scrape-gov-intern
npm run scrape-gov-jobs
```

### Command Line with Options:
```bash
# Scrape all pages of a category
node bursary-scraper.js bursaries

# Limit to first 5 pages
node bursary-scraper.js internships 5

# Show available categories
node bursary-scraper.js --help
```

### Quick Testing:
```bash
# Test with limited pages
npm run test-scraper

# Quick test (first 2 pages, 3 jobs per page)
npm run quick-test
```

### Use as a Module:
```javascript
const { scrapeJobs } = require('./bursary-scraper');

async function main() {
    // Scrape bursaries with no page limit
    const result = await scrapeJobs('bursaries');
    console.log(`Found ${result.jobs.length} jobs`);
    console.log('Metrics:', result.metrics);

    // Scrape internships, limit to 3 pages
    const limitedResult = await scrapeJobs('internships', 3);
}

main();
```

## Available Categories

| Category | URL | Description |
|----------|-----|-------------|
| `bursaries` | https://www.spane4all.co.za/bursaries | Educational funding opportunities |
| `internships` | https://spane4all.co.za/internships | Internship positions |
| `jobs` | https://spane4all.co.za/jobs | Regular job openings |
| `scholarships` | https://www.spane4all.co.za/scholarships | Scholarship opportunities |
| `general_jobs` | https://www.spane4all.co.za/general_jobs | General employment |
| `government_intern` | https://www.spane4all.co.za/government_intern | Government internships |
| `government_job` | https://www.spane4all.co.za/government_job | Government positions |

## Output

The scraper saves results to JSON files with the format: `{category}_YYYY-MM-DDTHH-mm-ss-sssZ.json`

### Job Data Structure
Each job entry contains:
```json
{
  "title": "Data Associate, Data/Content Management",
  "organization": "S&P Global",
  "employmentType": "job",
  "jobLocation": "Centurion, South Africa",
  "baseSalary": "Market Related",
  "closingDate": "31-07-2025",
  "applyUrl": "https://careers.spglobal.com/jobs/300274",
  "sourceUrl": "https://www.spane4all.co.za/job/6796",
  "image": "/uploaded_img/693883.jpeg",
  "description": "The Data Analyst role contributes to the business..."
}
```

### Metrics Output
The scraper provides comprehensive metrics:
```json
{
  "totalJobs": 115,
  "successfulJobs": 110,
  "failedJobs": 5,
  "totalPagesScraped": 13,
  "totalTimeSeconds": 245.67,
  "avgTimePerJob": 2.23,
  "filename": "bursaries_2025-07-02T15-00-26-081Z.json",
  "category": "bursaries"
}
```

## Configuration

You can modify the scraper behavior by editing `bursary-scraper.js`:

- Set `headless: true` in the browser launch options for headless mode
- Adjust timeouts and wait conditions as needed
- Modify the data extraction logic in the `page.evaluate()` function

## Error Handling

The scraper includes robust error handling:
- Continues scraping even if individual jobs fail
- Automatically navigates back to the main page if stuck
- Logs detailed error information
- Saves partial results even if the scraper encounters issues

## Notes

- The scraper runs in non-headless mode by default so you can see the browser actions
- It waits for network idle state to ensure pages are fully loaded
- Pagination is handled automatically by looking for "Next" buttons
- The scraper respects the website's structure and waits appropriately between actions
