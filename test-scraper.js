const { scrapeBursaries } = require('./bursary-scraper');

async function testScraper() {
    console.log('Starting bursary scraper test...');
    try {
        const results = await scrapeBursaries();
        console.log(`Test completed successfully! Scraped ${results.length} jobs.`);
        
        // Display first few results as sample
        if (results.length > 0) {
            console.log('\nSample results:');
            results.slice(0, 3).forEach((job, index) => {
                console.log(`\n--- Job ${index + 1} ---`);
                console.log(`Organization: ${job.organization || 'N/A'}`);
                console.log(`Employment Type: ${job.employmentType || 'N/A'}`);
                console.log(`Location: ${job.jobLocation || 'N/A'}`);
                console.log(`Salary: ${job.baseSalary || 'N/A'}`);
                console.log(`Closing Date: ${job.closingDate || 'N/A'}`);
                console.log(`Apply URL: ${job.applyUrl || 'N/A'}`);
                console.log(`Source URL: ${job.sourceUrl || 'N/A'}`);
            });
        }
    } catch (error) {
        console.error('Test failed:', error);
    }
}

testScraper();
